/**
 * Promotion Service
 * Business logic cho Promotion/Discount management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import {
  PromotionRepository,
  ProductRepository,
  UserRepository,
} from "../repositories";
import {
  PromotionEntity,
  CreatePromotionData,
  UpdatePromotionData,
  PromotionBusinessRules,
} from "../../models/promotion.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const PROMOTION_SERVICE = Symbol("PromotionService");

@Injectable
export class PromotionService extends BaseService {
  private promotionRepository: PromotionRepository;
  private productRepository: ProductRepository;
  private userRepository: UserRepository;

  constructor(
    promotionRepository: PromotionRepository,
    productRepository: ProductRepository,
    userRepository: UserRepository
  ) {
    super();
    this.promotionRepository = promotionRepository;
    this.productRepository = productRepository;
    this.userRepository = userRepository;
  }

  /**
   * Tạo promotion mới
   */
  async createPromotion(
    data: CreatePromotionData,
    createdBy: UserEntity
  ): Promise<PromotionEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can create promotions");
      }

      // Validate input
      this.validateRequired(data, [
        "name",
        "type",
        "value",
        "startDate",
        "endDate",
      ]);

      // Validate promotion data
      const validation = PromotionBusinessRules.validatePromotionData(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check code uniqueness if provided
      if (data.code) {
        const existingPromotion = await this.promotionRepository.findByCode(
          data.code
        );
        if (existingPromotion) {
          throw new ConflictError(
            `Promotion with code '${data.code}' already exists`
          );
        }
      }

      // Validate date range
      if (new Date(data.startDate) >= new Date(data.endDate)) {
        throw new ValidationError("Start date must be before end date");
      }

      // Create promotion
      const promotionData = {
        ...data,
        code: data.code || this.generateId().substring(0, 8).toUpperCase(),
        status: "ACTIVE",
        usageCount: 0,
      };

      const promotion = (await this.promotionRepository.create(
        promotionData
      )) as unknown as PromotionEntity;

      // Log activity
      await this.logActivity("PROMOTION_CREATED", createdBy.id, {
        promotionId: promotion.id,
        name: promotion.name,
        code: promotion.code,
        type: promotion.type,
      });

      return promotion;
    }, "createPromotion");
  }

  /**
   * Lấy promotion theo ID
   */
  async getPromotionById(
    id: string,
    requestedBy?: UserEntity
  ): Promise<PromotionEntity> {
    return this.executeWithErrorHandling(async () => {
      const promotion = await this.promotionRepository.findById(id);
      if (!promotion) {
        throw new NotFoundError("Promotion", id);
      }

      // Check if promotion is active or user has permission
      if (!promotion.isActive && (!requestedBy || !this.isAdmin(requestedBy))) {
        throw new ForbiddenError("Cannot access inactive promotion");
      }

      return promotion as unknown as PromotionEntity;
    }, "getPromotionById");
  }

  /**
   * Lấy promotion theo code
   */
  async getPromotionByCode(code: string): Promise<PromotionEntity | null> {
    return this.executeWithErrorHandling(async () => {
      const promotion = await this.promotionRepository.findFirst({
        where: { code },
      });
      if (!promotion) {
        return null;
      }

      // Check if promotion is active and valid
      const now = new Date();
      if (
        !promotion.isActive ||
        now < promotion.startDate ||
        (promotion.endDate && now > promotion.endDate)
      ) {
        return null;
      }

      return promotion as unknown as PromotionEntity;
    }, "getPromotionByCode");
  }

  /**
   * Cập nhật promotion
   */
  async updatePromotion(
    id: string,
    data: UpdatePromotionData,
    updatedBy: UserEntity
  ): Promise<PromotionEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update promotions");
      }

      // Check if promotion exists
      const existingPromotion = await this.promotionRepository.findById(id);
      if (!existingPromotion) {
        throw new NotFoundError("Promotion", id);
      }

      // Validate code if provided
      if (data.code && data.code !== existingPromotion.code) {
        const promotionWithCode = await this.promotionRepository.findByCode(
          data.code
        );
        if (promotionWithCode) {
          throw new ConflictError(
            `Promotion with code '${data.code}' already exists`
          );
        }
      }

      // Validate date range if provided
      const startDate = data.startDate
        ? new Date(data.startDate)
        : new Date(existingPromotion.startDate);
      const endDate = data.endDate
        ? new Date(data.endDate)
        : new Date(existingPromotion.endDate || new Date());
      if (startDate >= endDate) {
        throw new ValidationError("Start date must be before end date");
      }

      // Update promotion
      const updatedPromotion = (await this.promotionRepository.update(
        id,
        data
      )) as unknown as PromotionEntity;

      // Log activity
      await this.logActivity("PROMOTION_UPDATED", updatedBy.id, {
        promotionId: id,
        changes: Object.keys(data),
      });

      return updatedPromotion;
    }, "updatePromotion");
  }

  /**
   * Xóa promotion
   */
  async deletePromotion(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete promotions");
      }

      // Check if promotion exists
      const promotion = await this.promotionRepository.findById(id);
      if (!promotion) {
        throw new NotFoundError("Promotion", id);
      }

      // Check if promotion can be deleted
      if (promotion.usageCount > 0) {
        throw new ValidationError("Cannot delete promotion that has been used");
      }

      // Delete promotion
      await this.promotionRepository.delete(id);

      // Log activity
      await this.logActivity("PROMOTION_DELETED", deletedBy.id, {
        promotionId: id,
        name: promotion.name,
        code: promotion.code,
      });
    }, "deletePromotion");
  }

  /**
   * Lấy danh sách promotions
   */
  async getPromotions(
    filters: SearchFilters & {
      status?: string;
      type?: string;
      active?: boolean;
    } = {},
    requestedBy?: UserEntity
  ): Promise<PaginatedResult<PromotionEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      // Non-admin users can only see active promotions
      if (!requestedBy || !this.isAdmin(requestedBy)) {
        searchConditions.isActive = true;
        searchConditions.startDate = { lte: new Date() };
        searchConditions.endDate = { gte: new Date() };
      } else {
        if (filters.active !== undefined) {
          searchConditions.isActive = filters.active;
          if (filters.active) {
            searchConditions.startDate = { lte: new Date() };
            searchConditions.endDate = { gte: new Date() };
          }
        }
      }

      if (filters.type) {
        searchConditions.type = filters.type;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { name: { contains: searchQuery, mode: "insensitive" } },
          { code: { contains: searchQuery, mode: "insensitive" } },
          { description: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      // Get promotions with pagination
      const result = await this.promotionRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
      });

      return {
        data: result.data.map(
          (promotion) => promotion as unknown as PromotionEntity
        ),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getPromotions");
  }

  /**
   * Apply promotion to order
   */
  async applyPromotion(
    code: string,
    orderTotal: number,
    userId?: string,
    productIds?: string[]
  ): Promise<{
    valid: boolean;
    discount: number;
    message?: string;
  }> {
    return this.executeWithErrorHandling(async () => {
      const promotion = await this.getPromotionByCode(code);
      if (!promotion) {
        return {
          valid: false,
          discount: 0,
          message: "Invalid promotion code",
        };
      }

      // Check promotion validity
      const validationResult = PromotionBusinessRules.canUsePromotion(
        promotion,
        userId || "",
        orderTotal,
        0 // userUsageCount - simplified
      );

      if (!validationResult.canUse) {
        return {
          valid: false,
          discount: 0,
          message: validationResult.reason || "Cannot use promotion",
        };
      }

      // Calculate discount
      const discount = PromotionBusinessRules.calculateDiscount(
        promotion,
        orderTotal
      );

      return {
        valid: true,
        discount,
        message: "Promotion applied successfully",
      };
    }, "applyPromotion");
  }

  /**
   * Use promotion (increment usage count)
   */
  async usePromotion(
    code: string,
    userId?: string,
    orderId?: string
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const promotion = await this.promotionRepository.findByCode(code);
      if (!promotion) {
        throw new NotFoundError("Promotion", code);
      }

      // Increment usage count
      await this.promotionRepository.update(promotion.id, {
        usageCount: promotion.usageCount + 1,
      });

      // Log usage
      await this.logActivity("PROMOTION_USED", userId || "system", {
        promotionId: promotion.id,
        code: promotion.code,
        orderId,
      });
    }, "usePromotion");
  }

  /**
   * Activate promotion
   */
  async activatePromotion(
    id: string,
    activatedBy: UserEntity
  ): Promise<PromotionEntity> {
    return this.executeWithErrorHandling(async () => {
      const promotion = await this.updatePromotion(
        id,
        { isActive: true },
        activatedBy
      );

      // Log activity
      await this.logActivity("PROMOTION_ACTIVATED", activatedBy.id, {
        promotionId: id,
      });

      return promotion;
    }, "activatePromotion");
  }

  /**
   * Deactivate promotion
   */
  async deactivatePromotion(
    id: string,
    deactivatedBy: UserEntity
  ): Promise<PromotionEntity> {
    return this.executeWithErrorHandling(async () => {
      const promotion = await this.updatePromotion(
        id,
        { isActive: false },
        deactivatedBy
      );

      // Log activity
      await this.logActivity("PROMOTION_DEACTIVATED", deactivatedBy.id, {
        promotionId: id,
      });

      return promotion;
    }, "deactivatePromotion");
  }

  /**
   * Get active promotions
   */
  async getActivePromotions(): Promise<PromotionEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const now = new Date();
      const promotions = await this.promotionRepository.findMany({
        where: {
          status: "ACTIVE",
          startDate: { lte: now },
          endDate: { gte: now },
        },
        orderBy: { createdAt: "desc" },
      });

      return promotions.map((p) => p as unknown as PromotionEntity);
    }, "getActivePromotions");
  }

  /**
   * Get promotion statistics
   */
  async getPromotionStats(
    id: string,
    requestedBy: UserEntity
  ): Promise<{
    usageCount: number;
    totalDiscount: number;
    conversionRate: number;
    topUsers: { userId: string; usageCount: number }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can view promotion statistics");
      }

      // Get basic stats (simplified implementation)
      const usageCount = await this.promotionRepository.count({
        where: { id },
      });

      return {
        usageCount,
        totalDiscount: 0,
        conversionRate: 0,
        topUsers: [],
      };
    }, "getPromotionStats");
  }

  /**
   * Bulk update promotion status
   */
  async bulkUpdatePromotionStatus(
    ids: string[],
    status: "ACTIVE" | "INACTIVE",
    updatedBy: UserEntity
  ): Promise<{
    success: string[];
    failed: { id: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError(
          "Only admins can bulk update promotion status"
        );
      }

      const success: string[] = [];
      const failed: { id: string; reason: string }[] = [];

      for (const id of ids) {
        try {
          await this.updatePromotion(
            id,
            { isActive: status === "ACTIVE" },
            updatedBy
          );
          success.push(id);
        } catch (error) {
          failed.push({
            id,
            reason: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Log activity
      await this.logActivity("PROMOTIONS_BULK_STATUS_UPDATED", updatedBy.id, {
        successCount: success.length,
        failedCount: failed.length,
        newStatus: status,
      });

      return { success, failed };
    }, "bulkUpdatePromotionStatus");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
