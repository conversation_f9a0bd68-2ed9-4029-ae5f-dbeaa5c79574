/**
 * Attribute Service
 * Business logic cho Product Attribute management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { AttributeRepository, ProductRepository } from "../repositories";
import {
  AttributeEntity,
  CreateAttributeData,
  UpdateAttributeData,
  AttributeBusinessRules,
} from "../../models/attribute.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const ATTRIBUTE_SERVICE = Symbol("AttributeService");

@Injectable
export class AttributeService extends BaseService {
  private attributeRepository: AttributeRepository;
  private productRepository: ProductRepository;

  constructor(
    attributeRepository: AttributeRepository,
    productRepository: ProductRepository
  ) {
    super();
    this.attributeRepository = attributeRepository;
    this.productRepository = productRepository;
  }

  /**
   * Tạo attribute mới
   */
  async createAttribute(
    data: CreateAttributeData,
    createdBy: UserEntity
  ): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(createdBy)) {
        throw new ForbiddenError("Only admins can create attributes");
      }

      // Validate input
      this.validateRequired(data, ["name", "type"]);

      // Validate attribute data
      const validation = AttributeBusinessRules.validateAttributeData(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check name uniqueness
      const existingAttribute = await this.attributeRepository.findByName(
        data.name
      );
      if (existingAttribute) {
        throw new ConflictError(
          `Attribute with name '${data.name}' already exists`
        );
      }

      // Validate values for certain types
      if (data.type === "SELECT" || data.type === "MULTI_SELECT") {
        if (!data.values || data.values.length === 0) {
          throw new ValidationError(
            "Select attributes must have predefined values"
          );
        }
      }

      // Create attribute
      const attributeData = {
        name: data.name,
        slug: AttributeBusinessRules.generateSlug(data.name),
        type: data.type,
        isRequired: data.isRequired || false,
        isFilterable: data.isFilterable || false,
        sortOrder: data.sortOrder || 0,
        metadata: data.metadata,
        // Handle values as relation
        values: data.values
          ? {
              create: data.values.map((value, index) => ({
                value,
                slug: AttributeBusinessRules.generateSlug(value),
                sortOrder: index,
              })),
            }
          : undefined,
      };

      const attribute = (await this.attributeRepository.create(
        attributeData
      )) as unknown as AttributeEntity;

      // Log activity
      await this.logActivity("ATTRIBUTE_CREATED", createdBy.id, {
        attributeId: attribute.id,
        name: attribute.name,
        type: attribute.type,
      });

      return attribute;
    }, "createAttribute");
  }

  /**
   * Lấy attribute theo ID
   */
  async getAttributeById(id: string): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      const attribute = await this.attributeRepository.findById(id);
      if (!attribute) {
        throw new NotFoundError("Attribute", id);
      }

      return attribute as unknown as AttributeEntity;
    }, "getAttributeById");
  }

  /**
   * Lấy attribute theo slug
   */
  async getAttributeBySlug(slug: string): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      const attribute = await this.attributeRepository.findBySlug(slug);
      if (!attribute) {
        throw new NotFoundError("Attribute", slug);
      }

      return attribute as unknown as AttributeEntity;
    }, "getAttributeBySlug");
  }

  /**
   * Cập nhật attribute
   */
  async updateAttribute(
    id: string,
    data: UpdateAttributeData,
    updatedBy: UserEntity
  ): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can update attributes");
      }

      // Check if attribute exists
      const existingAttribute = await this.attributeRepository.findById(id);
      if (!existingAttribute) {
        throw new NotFoundError("Attribute", id);
      }

      // Validate name if provided
      if (data.name && data.name !== existingAttribute.name) {
        const attributeWithName = await this.attributeRepository.findByName(
          data.name
        );
        if (attributeWithName) {
          throw new ConflictError(
            `Attribute with name '${data.name}' already exists`
          );
        }
      }

      // Validate type change
      if (data.type && data.type !== existingAttribute.type) {
        // Check if attribute is used in products
        const isUsed = await this.attributeRepository.isAttributeUsed(id);
        if (isUsed) {
          throw new ValidationError(
            "Cannot change type of attribute that is already used in products"
          );
        }
      }

      // Prepare update data
      let updateData: any = {
        name: data.name,
        type: data.type,
        isRequired: data.isRequired,
        isFilterable: data.isFilterable,
        sortOrder: data.sortOrder,
        metadata: data.metadata,
      };

      if (data.name) {
        updateData.slug = AttributeBusinessRules.generateSlug(data.name);
      }

      // Handle values update
      if (data.values) {
        updateData.values = {
          deleteMany: {}, // Delete all existing values
          create: data.values.map((value, index) => ({
            value,
            slug: AttributeBusinessRules.generateSlug(value),
            sortOrder: index,
          })),
        };
      }

      // Update attribute
      const updatedAttribute = (await this.attributeRepository.update(
        id,
        updateData
      )) as unknown as AttributeEntity;

      // Log activity
      await this.logActivity("ATTRIBUTE_UPDATED", updatedBy.id, {
        attributeId: id,
        changes: Object.keys(data),
      });

      return updatedAttribute;
    }, "updateAttribute");
  }

  /**
   * Xóa attribute
   */
  async deleteAttribute(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Only admins can delete attributes");
      }

      // Check if attribute exists
      const attribute = await this.attributeRepository.findById(id);
      if (!attribute) {
        throw new NotFoundError("Attribute", id);
      }

      // Check if attribute is used in products
      const isUsed = await this.attributeRepository.isAttributeUsed(id);
      if (isUsed) {
        throw new ValidationError(
          "Cannot delete attribute that is used in products"
        );
      }

      // Delete attribute
      await this.attributeRepository.delete(id);

      // Log activity
      await this.logActivity("ATTRIBUTE_DELETED", deletedBy.id, {
        attributeId: id,
        name: attribute.name,
      });
    }, "deleteAttribute");
  }

  /**
   * Lấy danh sách attributes
   */
  async getAttributes(
    filters: SearchFilters & {
      type?: string;
      isRequired?: boolean;
      isFilterable?: boolean;
    } = {}
  ): Promise<PaginatedResult<AttributeEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 50;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.type) {
        searchConditions.type = filters.type;
      }

      if (filters.isRequired !== undefined) {
        searchConditions.isRequired = filters.isRequired;
      }

      if (filters.isFilterable !== undefined) {
        searchConditions.isFilterable = filters.isFilterable;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { name: { contains: searchQuery, mode: "insensitive" } },
          { description: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      // Get attributes with pagination
      const result = await this.attributeRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "sortOrder"]: filters.sortOrder || "asc",
        },
      });

      return {
        data: result.data.map(
          (attribute) => attribute as unknown as AttributeEntity
        ),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1,
      };
    }, "getAttributes");
  }

  /**
   * Get filterable attributes
   */
  async getFilterableAttributes(): Promise<AttributeEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const attributes = await this.attributeRepository.findMany({
        where: { isFilterable: true },
        orderBy: { sortOrder: "asc" },
      });

      return attributes.map((attr) => attr as unknown as AttributeEntity);
    }, "getFilterableAttributes");
  }

  /**
   * Get required attributes
   */
  async getRequiredAttributes(): Promise<AttributeEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const attributes = await this.attributeRepository.findMany({
        where: { isRequired: true },
        orderBy: { sortOrder: "asc" },
      });

      return attributes.map((attr) => attr as unknown as AttributeEntity);
    }, "getRequiredAttributes");
  }

  /**
   * Update attribute sort order
   */
  async updateSortOrder(
    id: string,
    sortOrder: number,
    updatedBy: UserEntity
  ): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      const attribute = await this.updateAttribute(
        id,
        { sortOrder },
        updatedBy
      );

      // Log activity
      await this.logActivity("ATTRIBUTE_SORT_ORDER_UPDATED", updatedBy.id, {
        attributeId: id,
        sortOrder,
      });

      return attribute;
    }, "updateSortOrder");
  }

  /**
   * Add attribute value
   */
  async addAttributeValue(
    id: string,
    value: string,
    updatedBy: UserEntity
  ): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can add attribute values");
      }

      const existingAttribute = await (
        this.attributeRepository as any
      ).findFirst({
        where: { id },
        include: { values: true },
      });
      if (!existingAttribute) {
        throw new NotFoundError("Attribute", id);
      }

      // Check if attribute supports values
      if (
        !["SELECT", "MULTI_SELECT", "COLOR", "SIZE"].includes(
          existingAttribute.type
        )
      ) {
        throw new ValidationError(
          "This attribute type does not support predefined values"
        );
      }

      // Add value to existing values
      const existingValues =
        existingAttribute.values?.map((v: any) => v.value) || [];
      if (!existingValues.includes(value)) {
        existingValues.push(value);
      }

      const attribute = await this.updateAttribute(
        id,
        { values: existingValues },
        updatedBy
      );

      // Log activity
      await this.logActivity("ATTRIBUTE_VALUE_ADDED", updatedBy.id, {
        attributeId: id,
        value,
      });

      return attribute;
    }, "addAttributeValue");
  }

  /**
   * Remove attribute value
   */
  async removeAttributeValue(
    id: string,
    value: string,
    updatedBy: UserEntity
  ): Promise<AttributeEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can remove attribute values");
      }

      const existingAttribute = await (
        this.attributeRepository as any
      ).findFirst({
        where: { id },
        include: { values: true },
      });
      if (!existingAttribute) {
        throw new NotFoundError("Attribute", id);
      }

      // Check if value is used in products
      const isValueUsed = await this.attributeRepository.isAttributeValueUsed(
        id,
        value
      );
      if (isValueUsed) {
        throw new ValidationError(
          "Cannot remove attribute value that is used in products"
        );
      }

      // Remove value from existing values
      const values = (
        existingAttribute.values?.map((v: any) => v.value) || []
      ).filter((v: string) => v !== value);

      const attribute = await this.updateAttribute(id, { values }, updatedBy);

      // Log activity
      await this.logActivity("ATTRIBUTE_VALUE_REMOVED", updatedBy.id, {
        attributeId: id,
        value,
      });

      return attribute;
    }, "removeAttributeValue");
  }

  /**
   * Get attribute usage statistics
   */
  async getAttributeUsageStats(
    id: string,
    requestedBy: UserEntity
  ): Promise<{
    totalProducts: number;
    valueUsage: { value: string; count: number }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError(
          "Only admins can view attribute usage statistics"
        );
      }

      const stats = await this.attributeRepository.getAttributeUsageStats(id);
      return stats;
    }, "getAttributeUsageStats");
  }

  /**
   * Bulk update attribute sort orders
   */
  async bulkUpdateSortOrders(
    updates: { id: string; sortOrder: number }[],
    updatedBy: UserEntity
  ): Promise<{
    success: string[];
    failed: { id: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Only admins can bulk update sort orders");
      }

      const success: string[] = [];
      const failed: { id: string; reason: string }[] = [];

      for (const update of updates) {
        try {
          await this.updateSortOrder(update.id, update.sortOrder, updatedBy);
          success.push(update.id);
        } catch (error) {
          failed.push({
            id: update.id,
            reason: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Log activity
      await this.logActivity(
        "ATTRIBUTES_SORT_ORDER_BULK_UPDATED",
        updatedBy.id,
        {
          successCount: success.length,
          failedCount: failed.length,
        }
      );

      return { success, failed };
    }, "bulkUpdateSortOrders");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
