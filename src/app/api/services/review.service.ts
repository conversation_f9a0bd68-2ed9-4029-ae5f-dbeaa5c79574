/**
 * Review Service
 * Business logic cho Review management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import {
  ReviewRepository,
  ProductRepository,
  UserRepository,
  OrderRepository,
} from "../repositories";
import { ReviewStatus as PrismaReviewStatus } from "@prisma/client";
import {
  ReviewEntity,
  CreateReviewData,
  UpdateReviewData,
  ReviewBusinessRules,
  ReviewStatus,
} from "../../models/review.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError,
  ConflictError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const REVIEW_SERVICE = Symbol("ReviewService");

@Injectable
export class ReviewService extends BaseService {
  private reviewRepository: ReviewRepository;
  private productRepository: ProductRepository;
  private userRepository: UserRepository;
  private orderRepository: OrderRepository;

  constructor(
    reviewRepository: ReviewRepository,
    productRepository: ProductRepository,
    userRepository: UserRepository,
    orderRepository: OrderRepository
  ) {
    super();
    this.reviewRepository = reviewRepository;
    this.productRepository = productRepository;
    this.userRepository = userRepository;
    this.orderRepository = orderRepository;
  }

  /**
   * Tạo review mới
   */
  async createReview(
    data: CreateReviewData,
    createdBy: UserEntity
  ): Promise<any> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ["productId", "rating"]);

      // Check if product exists
      const product = await this.productRepository.findById(data.productId);
      if (!product) {
        throw new NotFoundError("Product", data.productId);
      }

      // Validate rating
      if (!ReviewBusinessRules.validateRating(data.rating)) {
        throw new ValidationError("Rating must be between 1 and 5");
      }

      // Check if user has purchased this product (simplified check)
      // TODO: Implement proper purchase verification
      const hasPurchased = true; // For now, allow all users to review
      if (!hasPurchased && !this.isAdmin(createdBy)) {
        throw new ValidationError(
          "You can only review products you have purchased"
        );
      }

      // Check if user already reviewed this product
      const existingReview = await this.reviewRepository.findFirst({
        where: {
          userId: createdBy.id,
          productId: data.productId,
        },
      });
      if (existingReview) {
        throw new ConflictError("You have already reviewed this product");
      }

      // Validate review content
      const validation = ReviewBusinessRules.validateReview(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Create review
      const reviewData = {
        ...data,
        userId: createdBy.id,
        status: PrismaReviewStatus.PENDING, // Reviews need approval
      };

      const review = await this.reviewRepository.create(reviewData);

      // Update product rating
      await this.updateProductRating(data.productId);

      // Log activity
      await this.logActivity("REVIEW_CREATED", createdBy.id, {
        reviewId: review.id,
        productId: data.productId,
        rating: data.rating,
      });

      return review;
    }, "createReview");
  }

  /**
   * Lấy review theo ID
   */
  async getReviewById(id: string): Promise<ReviewEntity> {
    return this.executeWithErrorHandling(async () => {
      const review = await this.reviewRepository.findById(id);
      if (!review) {
        throw new NotFoundError("Review", id);
      }
      return review as unknown as ReviewEntity;
    }, "getReviewById");
  }

  /**
   * Cập nhật review
   */
  async updateReview(
    id: string,
    data: UpdateReviewData,
    updatedBy: UserEntity
  ): Promise<any> {
    return this.executeWithErrorHandling(async () => {
      // Check if review exists
      const existingReview = await this.reviewRepository.findById(id);
      if (!existingReview) {
        throw new NotFoundError("Review", id);
      }

      // Check permission
      if (existingReview.userId !== updatedBy.id && !this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Cannot update review of another user");
      }

      // Check if review can be updated
      const updateCheck = ReviewBusinessRules.canUpdate(
        existingReview as unknown as ReviewEntity,
        updatedBy.id
      );
      if (!updateCheck.canUpdate) {
        throw new ValidationError(
          updateCheck.reason || "Review cannot be updated"
        );
      }

      // Validate rating if provided
      if (data.rating && !ReviewBusinessRules.validateRating(data.rating)) {
        throw new ValidationError("Rating must be between 1 and 5");
      }

      // Validate review content
      if (data.comment || data.rating) {
        const mergedData = {
          ...existingReview,
          ...data,
          comment: data.comment || existingReview.comment || "",
          rating: data.rating || existingReview.rating,
        } as CreateReviewData;
        const validation = ReviewBusinessRules.validateReview(mergedData);
        if (!validation.valid) {
          throw new ValidationError(validation.errors.join(", "));
        }
      }

      // Update review
      // Convert status if needed
      const updateData = {
        ...data,
        status: data.status ? (data.status as any) : undefined,
      };

      const updatedReview = await this.reviewRepository.update(id, updateData);

      // Update product rating if rating changed
      if (data.rating) {
        await this.updateProductRating(existingReview.productId);
      }

      // Log activity
      await this.logActivity("REVIEW_UPDATED", updatedBy.id, {
        reviewId: id,
        changes: data,
      });

      return updatedReview;
    }, "updateReview");
  }

  /**
   * Xóa review
   */
  async deleteReview(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if review exists
      const review = await this.reviewRepository.findById(id);
      if (!review) {
        throw new NotFoundError("Review", id);
      }

      // Check permission
      if (review.userId !== deletedBy.id && !this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Cannot delete review of another user");
      }

      // Check if review can be deleted
      const deleteCheck = ReviewBusinessRules.canDelete(
        review as unknown as ReviewEntity,
        deletedBy.id
      );
      if (!deleteCheck.canDelete) {
        throw new ValidationError(
          deleteCheck.reason || "Review cannot be deleted"
        );
      }

      // Delete review
      await this.reviewRepository.delete(id);

      // Update product rating
      await this.updateProductRating(review.productId);

      // Log activity
      await this.logActivity("REVIEW_DELETED", deletedBy.id, {
        reviewId: id,
        productId: review.productId,
      });
    }, "deleteReview");
  }

  /**
   * Lấy reviews của product
   */
  async getProductReviews(
    productId: string,
    filters: SearchFilters & { rating?: number } = {}
  ): Promise<PaginatedResult<ReviewEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check if product exists
      const product = await this.productRepository.findById(productId);
      if (!product) {
        throw new NotFoundError("Product", productId);
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {
        productId,
        status: "APPROVED", // Only show approved reviews to public
      };

      if (filters.rating) {
        searchConditions.rating = filters.rating;
      }

      // Get reviews with pagination
      const result = await this.reviewRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      });

      return {
        data: result.data.map((review) => review as unknown as ReviewEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getProductReviews");
  }

  /**
   * Lấy reviews của user
   */
  async getUserReviews(
    userId: string,
    requestedBy: UserEntity,
    filters: SearchFilters = {}
  ): Promise<PaginatedResult<ReviewEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access reviews of another user");
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = { userId };

      if (filters.status) {
        searchConditions.status = filters.status;
      }

      // Get reviews with pagination
      const result = await this.reviewRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true,
              media: {
                include: { media: true },
                where: { isPrimary: true },
                take: 1,
              },
            },
          },
        },
      });

      return {
        data: result.data.map((review) => review as unknown as ReviewEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      };
    }, "getUserReviews");
  }

  /**
   * Approve review (admin only)
   */
  async approveReview(
    id: string,
    approvedBy: UserEntity
  ): Promise<ReviewEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(approvedBy)) {
        throw new ForbiddenError("Only admins can approve reviews");
      }

      // Check if review exists
      const review = await this.reviewRepository.findById(id);
      if (!review) {
        throw new NotFoundError("Review", id);
      }

      // Update review status
      const updatedReview = (await this.reviewRepository.update(id, {
        status: "APPROVED",
      })) as unknown as ReviewEntity;

      // Update product rating
      await this.updateProductRating(review.productId);

      // Log activity
      await this.logActivity("REVIEW_APPROVED", approvedBy.id, {
        reviewId: id,
        productId: review.productId,
      });

      return updatedReview;
    }, "approveReview");
  }

  /**
   * Reject review (admin only)
   */
  async rejectReview(
    id: string,
    reason: string,
    rejectedBy: UserEntity
  ): Promise<any> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(rejectedBy)) {
        throw new ForbiddenError("Only admins can reject reviews");
      }

      // Check if review exists
      const review = await this.reviewRepository.findById(id);
      if (!review) {
        throw new NotFoundError("Review", id);
      }

      // Update review status
      const updatedReview = await this.reviewRepository.update(id, {
        status: PrismaReviewStatus.REJECTED,
        // adminNote: reason, // Remove if not in schema
      });

      // Log activity
      await this.logActivity("REVIEW_REJECTED", rejectedBy.id, {
        reviewId: id,
        productId: review.productId,
        reason,
      });

      return updatedReview;
    }, "rejectReview");
  }

  /**
   * Lấy review statistics cho product
   */
  async getProductReviewStats(productId: string): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingDistribution: { [key: number]: number };
  }> {
    return this.executeWithErrorHandling(async () => {
      const stats = await this.reviewRepository.getReviewStats();
      return {
        totalReviews: stats.total,
        averageRating: stats.avgRating,
        ratingDistribution: stats.ratingDistribution.reduce(
          (acc: { [key: number]: number }, item: any) => {
            acc[item.rating] = item.count;
            return acc;
          },
          {}
        ),
      };
    }, "getProductReviewStats");
  }

  /**
   * Update product rating based on reviews
   */
  private async updateProductRating(productId: string): Promise<void> {
    const stats = await this.reviewRepository.getReviewStats();
    await this.productRepository.update(productId, {
      // rating: stats.averageRating, // Remove if not in schema
      // reviewCount: stats.totalReviews, // Remove if not in schema
    });
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role as string);
  }
}
